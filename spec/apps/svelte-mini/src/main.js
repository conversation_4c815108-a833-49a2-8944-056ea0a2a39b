import * as Sentry from "@sentry/svelte";
import App from './App.svelte'

Sentry.init({
  dsn: import.meta.env.SENTRY_DSN_JS,
  debug: true,
  integrations: [
    Sentry.browserTracingIntegration({
      tracePropagationTargets: ["localhost", /^http:\/\/localhost:4000/],
    }),
  ],
  tracesSampleRate: 1.0,
  environment: "test",
});

const app = new App({
  target: document.getElementById('app'),
})

export default app
