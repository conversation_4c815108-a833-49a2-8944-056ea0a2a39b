# frozen_string_literal: true

module Test
  module Helper
    module_function

    def logged_events
      @logged_events ||= begin
        extracted_events = []
        envelopes = []

        logged_envelopes.each do |event_data|
          envelopes << {
            "headers" => event_data["envelope_headers"],
            "items" => event_data["items"]
          }

          event_data["items"].each do |item|
            if ["event", "transaction"].include?(item["headers"]["type"])
              extracted_events << item["payload"]
            end
          end
        end

        {
          events: extracted_events,
          envelopes: envelopes,
          event_count: extracted_events.length,
          envelope_count: envelopes.length
        }
      end
    end

    def logged_envelopes
      # For e2e tests, read from the Rails app's debug log file since the test
      # and Rails app are separate processes with separate Sentry clients
      log_file_path = File.join("spec", "apps", "rails-mini", "log", "sentry_debug_events.log")

      return [] unless File.exist?(log_file_path)

      File.readlines(log_file_path).map do |line|
        JSON.parse(line.strip)
      rescue JSON::ParserError
        nil
      end.compact
    end

    def clear_logged_events
      # Clear the Rails app's debug log file and reset cached events
      log_file_path = File.join("spec", "apps", "rails-mini", "log", "sentry_debug_events.log")
      File.write(log_file_path, "") if File.exist?(log_file_path)
      @logged_events = nil # Reset cached events
    end

    def clear_logged_events
      # Clear the Rails app's debug log file
      log_file_path = File.join("spec", "apps", "rails-mini", "log", "sentry_debug_events.log")
      File.write(log_file_path, "") if File.exist?(log_file_path)
      @logged_events = nil # Reset cached events
    end

    # TODO: move this to a shared helper for all gems
    def perform_basic_setup
      Sentry.init do |config|
        config.sdk_logger = Logger.new(nil)
        config.dsn = Sentry::TestHelper::DUMMY_DSN
        config.transport.transport_class = Sentry::DummyTransport
        # so the events will be sent synchronously for testing
        config.background_worker_threads = 0
        yield(config) if block_given?
      end
    end
  end
end
