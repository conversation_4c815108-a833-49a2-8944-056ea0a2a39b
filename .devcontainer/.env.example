# Official Ruby images
IMAGE="ruby:3.4.5-slim-bookworm"
VERSION="3.4.5"

# IMAGE="jruby:latest"

# E2E testing
SENTRY_DSN="http://12345:<EMAIL>/sentry/42"
SENTRY_DSN_JS="http://12345:<EMAIL>/sentry/43"

SENTRY_E2E_RAILS_APP_PORT=4000
SENTRY_E2E_SVELTE_APP_PORT=4001

SENTRY_E2E_RAILS_APP_URL="http://localhost:4000"
SENTRY_E2E_SVELTE_APP_URL="http://localhost:4001"

# Faster builds with compose
COMPOSE_BAKE=true
